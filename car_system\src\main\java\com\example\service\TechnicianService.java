package com.example.service;

import com.example.config.AppProperties;
import com.example.dto.request.TechnicianRegistrationRequest;
import com.example.dto.request.TechnicianUpdateRequest;
import com.example.dto.response.PageResponse;
import com.example.dto.response.SpecialtyDTO;
import com.example.dto.response.TechnicianDTO;
import com.example.dto.response.OrderDTO;
import com.example.dto.response.LaborPaymentDTO;
import com.example.dto.response.TechnicianStatsDTO;
import com.example.entity.Technician;
import com.example.entity.RepairOrder;
import com.example.entity.LaborPayment;
import com.example.entity.OrderTechnicianAssignment;
import com.example.entity.OrderFeedback;
import com.example.exception.BusinessException;
import com.example.exception.ResourceNotFoundException;
import com.example.repository.TechnicianRepository;
import com.example.repository.RepairOrderRepository;
import com.example.repository.LaborPaymentRepository;
import com.example.repository.OrderFeedbackRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 技师服务类
 */
@Service
@Transactional
public class TechnicianService {

    @Autowired
    private TechnicianRepository technicianRepository;

    @Autowired
    private RepairOrderRepository repairOrderRepository;

    @Autowired
    private LaborPaymentRepository laborPaymentRepository;

    @Autowired
    private OrderFeedbackRepository orderFeedbackRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private AppProperties appProperties;

    /**
     * 技师注册
     */
    public TechnicianDTO registerTechnician(TechnicianRegistrationRequest request) {
        // 检查用户名是否已存在
        if (technicianRepository.existsByUsername(request.getUsername())) {
            throw new BusinessException("用户名已存在");
        }

        // 检查手机号是否已存在
        if (technicianRepository.existsByPhone(request.getPhone())) {
            throw new BusinessException("手机号已被注册");
        }

        // 检查邮箱是否已存在（如果提供了邮箱）
        if (request.getEmail() != null && !request.getEmail().trim().isEmpty()
            && technicianRepository.existsByEmail(request.getEmail())) {
            throw new BusinessException("邮箱已被注册");
        }

        // 验证工种是否有效
        Technician.Specialty specialty;
        try {
            specialty = Technician.Specialty.valueOf(request.getSpecialty().toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new BusinessException("无效的工种类型");
        }

        // 创建新技师
        Technician technician = new Technician();
        technician.setUsername(request.getUsername());
        technician.setPassword(passwordEncoder.encode(request.getPassword()));
        technician.setRealName(request.getRealName());
        technician.setPhone(request.getPhone());
        technician.setEmail(request.getEmail());
        technician.setSpecialty(specialty);
        technician.setHourlyRate(request.getHourlyRate());
        technician.setHireDate(request.getHireDate());
        technician.setStatus(1); // 默认启用状态
        technician.setWorkload(0); // 初始工作负载为0
        technician.setRating(BigDecimal.valueOf(5.0)); // 初始评分为5.0

        Technician savedTechnician = technicianRepository.save(technician);
        return convertToDTO(savedTechnician);
    }

    /**
     * 获取工种列表
     */
    @Transactional(readOnly = true)
    public List<SpecialtyDTO> getSpecialties() {
        return appProperties.getSpecialties().stream()
            .map(specialty -> new SpecialtyDTO(specialty.getCode(), specialty.getName()))
            .collect(Collectors.toList());
    }

    /**
     * 获取当前技师信息
     */
    @Transactional(readOnly = true)
    public TechnicianDTO getCurrentTechnician(Long technicianId) {
        Technician technician = technicianRepository.findById(technicianId)
            .orElseThrow(() -> new ResourceNotFoundException("技师不存在"));
        return convertToDTO(technician);
    }

    /**
     * 更新当前技师信息（工种不可修改）
     */
    public TechnicianDTO updateCurrentTechnician(Long technicianId, TechnicianUpdateRequest request) {
        Technician technician = technicianRepository.findById(technicianId)
            .orElseThrow(() -> new ResourceNotFoundException("技师不存在"));

        // 检查手机号是否被其他技师使用
        if (!technician.getPhone().equals(request.getPhone()) && technicianRepository.existsByPhone(request.getPhone())) {
            throw new BusinessException("手机号已被其他技师使用");
        }

        // 检查邮箱是否被其他技师使用
        if (request.getEmail() != null && !request.getEmail().trim().isEmpty()
            && !request.getEmail().equals(technician.getEmail())
            && technicianRepository.existsByEmail(request.getEmail())) {
            throw new BusinessException("邮箱已被其他技师使用");
        }

        // 更新技师信息（不包括用户名、密码、工种和时薪）
        technician.setRealName(request.getRealName());
        technician.setPhone(request.getPhone());
        technician.setEmail(request.getEmail());

        Technician updatedTechnician = technicianRepository.save(technician);
        return convertToDTO(updatedTechnician);
    }

    /**
     * 获取当前技师的工单列表
     */
    @Transactional(readOnly = true)
    public PageResponse<OrderDTO> getCurrentTechnicianOrders(Long technicianId, String status, String urgencyLevel,
                                                           Long faultTypeId, LocalDateTime startDate, LocalDateTime endDate,
                                                           Pageable pageable) {
        // 使用Specification构建复杂查询
        Specification<RepairOrder> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 技师分配条件
            Join<RepairOrder, OrderTechnicianAssignment> assignmentJoin = root.join("technicianAssignments");
            predicates.add(criteriaBuilder.equal(assignmentJoin.get("technician").get("technicianId"), technicianId));

            // 状态筛选
            if (status != null && !status.trim().isEmpty()) {
                try {
                    RepairOrder.OrderStatus orderStatus = RepairOrder.OrderStatus.valueOf(status.toUpperCase());
                    predicates.add(criteriaBuilder.equal(root.get("status"), orderStatus));
                } catch (IllegalArgumentException e) {
                    // 忽略无效的状态值
                }
            }

            // 紧急程度筛选
            if (urgencyLevel != null && !urgencyLevel.trim().isEmpty()) {
                try {
                    RepairOrder.UrgencyLevel urgency = RepairOrder.UrgencyLevel.valueOf(urgencyLevel.toUpperCase());
                    predicates.add(criteriaBuilder.equal(root.get("urgencyLevel"), urgency));
                } catch (IllegalArgumentException e) {
                    // 忽略无效的紧急程度值
                }
            }

            // 故障类型ID筛选
            if (faultTypeId != null) {
                predicates.add(criteriaBuilder.equal(root.get("faultType").get("faultTypeId"), faultTypeId));
            }

            // 时间范围筛选
            if (startDate != null && endDate != null) {
                predicates.add(criteriaBuilder.between(root.get("submitTime"), startDate, endDate));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        Page<RepairOrder> orderPage = repairOrderRepository.findAll(spec, pageable);

        List<OrderDTO> orderDTOs = orderPage.getContent().stream()
            .map(this::convertOrderToDTO)
            .collect(Collectors.toList());

        return PageResponse.success(orderDTOs, new PageResponse.PageInfo(
                orderPage.getNumber() + 1,
                orderPage.getSize(),
                orderPage.getTotalElements(),
                orderPage.getTotalPages()
        ));
    }

    /**
     * 根据工种查找可用技师
     */
    @Transactional(readOnly = true)
    public List<TechnicianDTO> findAvailableTechniciansBySpecialty(String specialtyCode, int limit) {
        Technician.Specialty specialty;
        try {
            specialty = Technician.Specialty.valueOf(specialtyCode.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new BusinessException("无效的工种类型");
        }

        List<Technician> technicians = technicianRepository.findAvailableTechniciansBySpecialty(specialty);

        return technicians.stream()
            .limit(limit)
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    /**
     * 将Technician实体转换为TechnicianDTO
     */
    private TechnicianDTO convertToDTO(Technician technician) {
        // 获取工种名称
        String specialtyName = appProperties.getSpecialties().stream()
            .filter(s -> s.getCode().equals(technician.getSpecialty().name().toLowerCase()))
            .findFirst()
            .map(AppProperties.Specialty::getName)
            .orElse(technician.getSpecialty().name());

        return new TechnicianDTO(
            technician.getTechnicianId(),
            technician.getUsername(),
            technician.getRealName(),
            technician.getPhone(),
            technician.getEmail(),
            technician.getSpecialty().name().toLowerCase(),
            specialtyName,
            technician.getHourlyRate(),
            technician.getHireDate(),
            technician.getStatus(),
            technician.getWorkload(),
            technician.getRating(),
            technician.getCreateTime(),
            technician.getUpdateTime()
        );
    }

    /**
     * 将RepairOrder实体转换为OrderDTO（完整版本）
     */
    private OrderDTO convertOrderToDTO(RepairOrder order) {
        OrderDTO dto = new OrderDTO();
        dto.setOrderId(order.getOrderId());
        dto.setUserId(order.getUser().getUserId());
        dto.setVehicleId(order.getVehicle().getVehicleId());
        dto.setFaultTypeId(order.getFaultType().getFaultTypeId());
        dto.setDescription(order.getDescription());
        dto.setUrgencyLevel(order.getUrgencyLevel().name().toLowerCase());
        dto.setSubmitTime(order.getSubmitTime());
        dto.setPreferredTime(order.getPreferredTime());
        dto.setEstimatedCompletionTime(order.getEstimatedCompletionTime());
        dto.setActualCompletionTime(order.getActualCompletionTime());
        dto.setStatus(order.getStatus().name().toLowerCase());
        dto.setPaymentStatus(order.getPaymentStatus().name().toLowerCase());
        dto.setContactPhone(order.getContactPhone());
        dto.setTotalLaborCost(order.getTotalLaborCost());
        dto.setTotalMaterialCost(order.getTotalMaterialCost());
        dto.setTotalCost(order.getTotalCost());
        dto.setWorkResult(order.getWorkResult());
        dto.setWorkingHours(order.getWorkingHours());

        // 设置关联对象信息
        dto.setUser(new OrderDTO.UserInfo(
            order.getUser().getUserId(),
            order.getUser().getUsername(),
            order.getUser().getRealName(),
            order.getUser().getPhone()
        ));

        dto.setVehicle(new OrderDTO.VehicleInfo(
            order.getVehicle().getVehicleId(),
            order.getVehicle().getLicensePlate(),
            order.getVehicle().getBrand(),
            order.getVehicle().getModel()
        ));

        // 转换工种枚举为字符串列表
        List<String> specialtyStrings = order.getFaultType().getRequiredSpecialties().stream()
            .map(specialty -> specialty.name().toLowerCase())
            .collect(Collectors.toList());

        dto.setFaultType(new OrderDTO.FaultTypeInfo(
            order.getFaultType().getFaultTypeId(),
            order.getFaultType().getTypeName(),
            specialtyStrings,
            order.getFaultType().getEstimatedHours()
        ));

        // 分配的技师信息
        List<OrderDTO.TechnicianInfo> technicianInfos = order.getAssignedTechnicians().stream()
                .map(tech -> new OrderDTO.TechnicianInfo(
                        tech.getTechnicianId(),
                        tech.getRealName(),
                        tech.getSpecialty().name().toLowerCase(),
                        tech.getPhone()
                ))
                .collect(Collectors.toList());
        dto.setAssignedTechnicians(technicianInfos);

        // 设置技师分配信息（包含同意状态）
        List<OrderDTO.TechnicianAssignmentInfo> assignmentInfos = order.getTechnicianAssignments().stream()
                .map(assignment -> new OrderDTO.TechnicianAssignmentInfo(
                        assignment.getTechnician().getTechnicianId(),
                        assignment.getTechnician().getRealName(),
                        assignment.getTechnician().getSpecialty().name().toLowerCase(),
                        assignment.getTechnician().getPhone(),
                        assignment.getAgreementStatus().name().toLowerCase(),
                        assignment.getResponseTime()
                ))
                .collect(Collectors.toList());
        dto.setTechnicianAssignments(assignmentInfos);

        // 材料使用信息（技师可以查看分配给自己的工单的材料使用情况）
        if (order.getMaterialUsages() != null && !order.getMaterialUsages().isEmpty()) {
            List<OrderDTO.MaterialUsageInfo> materialUsageInfos = order.getMaterialUsages().stream()
                    .map(usage -> new OrderDTO.MaterialUsageInfo(
                            usage.getUsageId(),
                            usage.getMaterial().getMaterialId(),
                            usage.getMaterial().getMaterialName(),
                            usage.getMaterial().getSpecification(),
                            usage.getMaterial().getUnit(),
                            usage.getMaterial().getUnitPrice(),
                            usage.getQuantity(),
                            usage.getTotalPrice(),
                            usage.getUseTime()
                    ))
                    .collect(Collectors.toList());
            dto.setMaterialUsages(materialUsageInfos);
        }

        // 反馈信息
        if (order.getFeedback() != null) {
            OrderFeedback feedback = order.getFeedback();
            dto.setFeedback(new OrderDTO.FeedbackInfo(
                    feedback.getFeedbackId(),
                    feedback.getRating(),
                    feedback.getComment(),
                    feedback.getFeedbackTime()
            ));
        }

        // 催单信息
        if (order.getUrgentRequests() != null && !order.getUrgentRequests().isEmpty()) {
            List<OrderDTO.UrgentRequestInfo> urgentRequestInfos = order.getUrgentRequests().stream()
                    .map(urgentRequest -> new OrderDTO.UrgentRequestInfo(
                            urgentRequest.getUrgentId(),
                            urgentRequest.getReason(),
                            urgentRequest.getUrgentTime(),
                            urgentRequest.getStatus().name().toLowerCase(),
                            urgentRequest.getStatus().getDisplayName()
                    ))
                    .collect(Collectors.toList());
            dto.setUrgentRequests(urgentRequestInfos);
        }

        return dto;
    }

    /**
     * 获取当前技师的工时费收入记录
     */
    @Transactional(readOnly = true)
    public PageResponse<LaborPaymentDTO> getCurrentTechnicianPayments(Long technicianId, LocalDateTime startDate,
                                                                    LocalDateTime endDate, Pageable pageable) {
        // 使用Specification构建复杂查询
        Specification<LaborPayment> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 技师ID条件
            predicates.add(criteriaBuilder.equal(root.get("technician").get("technicianId"), technicianId));

            // 时间范围筛选
            if (startDate != null && endDate != null) {
                predicates.add(criteriaBuilder.between(root.get("createTime"), startDate, endDate));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        Page<LaborPayment> paymentPage = laborPaymentRepository.findAll(spec, pageable);

        List<LaborPaymentDTO> paymentDTOs = paymentPage.getContent().stream()
            .map(this::convertLaborPaymentToDTO)
            .collect(Collectors.toList());

        return PageResponse.success(paymentDTOs, new PageResponse.PageInfo(
                paymentPage.getNumber() + 1,
                paymentPage.getSize(),
                paymentPage.getTotalElements(),
                paymentPage.getTotalPages()
        ));
    }

    /**
     * 获取当前技师的历史记录（已完成的工单）
     */
    @Transactional(readOnly = true)
    public PageResponse<OrderDTO> getCurrentTechnicianHistory(Long technicianId, String status, Long faultTypeId,
                                                            LocalDateTime startDate, LocalDateTime endDate,
                                                            Pageable pageable) {
        // 使用Specification构建复杂查询
        Specification<RepairOrder> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 技师分配条件
            Join<RepairOrder, OrderTechnicianAssignment> assignmentJoin = root.join("technicianAssignments");
            predicates.add(criteriaBuilder.equal(assignmentJoin.get("technician").get("technicianId"), technicianId));

            // 默认只查询已完成或已取消的工单作为历史记录
            if (status != null && !status.trim().isEmpty()) {
                try {
                    RepairOrder.OrderStatus orderStatus = RepairOrder.OrderStatus.valueOf(status.toUpperCase());
                    predicates.add(criteriaBuilder.equal(root.get("status"), orderStatus));
                } catch (IllegalArgumentException e) {
                    // 如果状态无效，默认查询已完成和已取消的工单
                    predicates.add(root.get("status").in(
                        List.of(RepairOrder.OrderStatus.COMPLETED, RepairOrder.OrderStatus.CANCELLED)
                    ));
                }
            } else {
                // 默认查询已完成和已取消的工单
                predicates.add(root.get("status").in(
                    List.of(RepairOrder.OrderStatus.COMPLETED, RepairOrder.OrderStatus.CANCELLED)
                ));
            }

            // 故障类型ID筛选
            if (faultTypeId != null) {
                predicates.add(criteriaBuilder.equal(root.get("faultType").get("faultTypeId"), faultTypeId));
            }

            // 时间范围筛选
            if (startDate != null && endDate != null) {
                predicates.add(criteriaBuilder.between(root.get("actualCompletionTime"), startDate, endDate));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        Page<RepairOrder> historyPage = repairOrderRepository.findAll(spec, pageable);

        List<OrderDTO> historyDTOs = historyPage.getContent().stream()
            .map(this::convertOrderToDTO)
            .collect(Collectors.toList());

        return PageResponse.success(historyDTOs, new PageResponse.PageInfo(
                historyPage.getNumber() + 1,
                historyPage.getSize(),
                historyPage.getTotalElements(),
                historyPage.getTotalPages()
        ));
    }



    /**
     * 获取当前技师的统计数据
     */
    @Transactional(readOnly = true)
    public TechnicianStatsDTO getCurrentTechnicianStats(Long technicianId) {
        // 获取技师信息以获取时薪
        Technician technician = technicianRepository.findById(technicianId)
            .orElseThrow(() -> new RuntimeException("技师不存在"));
        BigDecimal hourlyRate = technician.getHourlyRate();

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime todayStart = now.toLocalDate().atStartOfDay();
        LocalDateTime todayEnd = now.toLocalDate().atTime(23, 59, 59);
        LocalDateTime monthStart = now.withDayOfMonth(1).toLocalDate().atStartOfDay();
        LocalDateTime monthEnd = now.withDayOfMonth(now.toLocalDate().lengthOfMonth()).toLocalDate().atTime(23, 59, 59);

        // 使用Specification构建查询
        Specification<RepairOrder> spec = (root, query, criteriaBuilder) -> {
            Join<RepairOrder, OrderTechnicianAssignment> assignmentJoin = root.join("technicianAssignments");
            return criteriaBuilder.equal(assignmentJoin.get("technician").get("technicianId"), technicianId);
        };

        List<RepairOrder> allOrders = repairOrderRepository.findAll(spec);

        // 今日统计
        int todayPending = 0;
        int todayInProgress = 0;
        int todayCompleted = 0;
        BigDecimal todayIncome = BigDecimal.ZERO;

        // 本月统计
        int monthlyCompleted = 0;
        BigDecimal monthlyHours = BigDecimal.ZERO;
        BigDecimal monthlyIncome = BigDecimal.ZERO;

        // 总体统计
        int totalCompleted = 0;
        BigDecimal totalHours = BigDecimal.ZERO;
        BigDecimal totalIncome = BigDecimal.ZERO;

        for (RepairOrder order : allOrders) {
            LocalDateTime completionTime = order.getActualCompletionTime();

            // 今日统计 - 统计当前状态的任务
            if (order.getStatus() == RepairOrder.OrderStatus.ASSIGNED) {
                todayPending++;
            } else if (order.getStatus() == RepairOrder.OrderStatus.IN_PROGRESS) {
                todayInProgress++;
            }

            // 今日完成的任务
            if (completionTime != null && completionTime.isAfter(todayStart) && completionTime.isBefore(todayEnd)) {
                if (order.getStatus() == RepairOrder.OrderStatus.COMPLETED) {
                    todayCompleted++;
                    // 计算该技师的收入：工时 × 时薪
                    if (order.getWorkingHours() != null && hourlyRate != null) {
                        BigDecimal technicianIncome = order.getWorkingHours().multiply(hourlyRate);
                        todayIncome = todayIncome.add(technicianIncome);
                    }
                }
            }

            // 本月统计
            if (completionTime != null && completionTime.isAfter(monthStart) && completionTime.isBefore(monthEnd)) {
                if (order.getStatus() == RepairOrder.OrderStatus.COMPLETED) {
                    monthlyCompleted++;
                    if (order.getWorkingHours() != null) {
                        monthlyHours = monthlyHours.add(order.getWorkingHours());
                    }
                    // 计算该技师的收入：工时 × 时薪
                    if (order.getWorkingHours() != null && hourlyRate != null) {
                        BigDecimal technicianIncome = order.getWorkingHours().multiply(hourlyRate);
                        monthlyIncome = monthlyIncome.add(technicianIncome);
                    }
                }
            }

            // 总体统计
            if (order.getStatus() == RepairOrder.OrderStatus.COMPLETED) {
                totalCompleted++;
                if (order.getWorkingHours() != null) {
                    totalHours = totalHours.add(order.getWorkingHours());
                }
                // 计算该技师的收入：工时 × 时薪
                if (order.getWorkingHours() != null && hourlyRate != null) {
                    BigDecimal technicianIncome = order.getWorkingHours().multiply(hourlyRate);
                    totalIncome = totalIncome.add(technicianIncome);
                }
            }
        }

        // 计算平均评分（这里需要从OrderFeedback表中获取）
        BigDecimal monthlyAvgRating = calculateMonthlyAvgRating(technicianId, monthStart, monthEnd);
        BigDecimal overallAvgRating = calculateOverallAvgRating(technicianId);

        return new TechnicianStatsDTO(
            todayPending, todayInProgress, todayCompleted, todayIncome,
            monthlyCompleted, monthlyHours, monthlyIncome, monthlyAvgRating,
            totalCompleted, totalHours, totalIncome, overallAvgRating
        );
    }

    /**
     * 计算技师本月平均评分
     */
    private BigDecimal calculateMonthlyAvgRating(Long technicianId, LocalDateTime monthStart, LocalDateTime monthEnd) {
        try {
            // 暂时使用总体平均评分，后续可以优化为按月计算
            return orderFeedbackRepository.calculateAverageRatingByTechnicianId(technicianId);
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 计算技师总体平均评分
     */
    private BigDecimal calculateOverallAvgRating(Long technicianId) {
        try {
            BigDecimal avgRating = orderFeedbackRepository.calculateAverageRatingByTechnicianId(technicianId);
            return avgRating != null ? avgRating : BigDecimal.ZERO;
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 将LaborPayment实体转换为LaborPaymentDTO
     */
    private LaborPaymentDTO convertLaborPaymentToDTO(LaborPayment payment) {
        return new LaborPaymentDTO(
            payment.getPaymentId(),
            payment.getTechnician().getTechnicianId(),
            payment.getYear(),
            payment.getMonth(),
            payment.getTotalHours(),
            payment.getHourlyRate(),
            payment.getTotalAmount(),
            payment.getPaymentStatus().name().toLowerCase(),
            payment.getPaymentTime(),
            payment.getCreateTime()
        );
    }
}
